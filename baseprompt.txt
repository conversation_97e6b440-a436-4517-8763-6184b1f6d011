User Context:
<PERSON><PERSON> egy angol nyelvű weblap repülőgép teljesíténydiagramok elemeinek annotációjához.

You will be provided with a visual logic graph described in JSON format. This graph consists of nodes and connections, where:
Nodes represent discrete functional units, each with a unique ID, a descriptive name, a detailed purpose, input ports, and output ports.

Connections define data flow by linking outputs of one node to inputs of another.

Your task is to:
Parse and understand the JSON structure representing the nodes and their connections.

Interpret each node's function based on its description and inputs/outputs.

Generate a coherent, working program that implements the entire logic defined by the graph.

Ensure the program correctly manages data flow between nodes as specified by the connections.

Implement input nodes to accept user data or events.

Implement output nodes to display results or trigger actions.

Handle errors, invalid inputs, and edge cases gracefully.

Structure your code modularly, reflecting each node as a separate function or class where appropriate.

Provide clear comments explaining key parts of the implementation.

Assume the target environment and technology stack can be chosen based on the node descriptions or specified requirements (for example: web app with HTML/JavaScript, desktop app in Python, etc.).
When given the JSON, always focus first on fully understanding the node roles and connections before coding. Ask clarifying questions if needed before proceeding.

The visual logic graph data is provided in the file: AnnotationWebpageLogic.json
Please load and analyze this JSON file to understand the node structure and connections, then implement the logic as a working program.