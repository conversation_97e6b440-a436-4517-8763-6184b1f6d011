{"nodes": [{"id": 1754937315274, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Egy gombbal betöltjük/tallózzuk a háttérképet a canvasra. A képet a canvas méretéhez igazítjuk az arány me<PERSON>.", "inputs": [], "outputs": [{"id": 1754937533938, "name": "k<PERSON>p"}]}, {"id": 1754937318913, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Ezt a gombot megnyomva mentjük az output JSON fájlt.\nTartalmazza:\ncanvason kirajzolt elemek ID-jét\ncanvason kirajzolt elemek tí<PERSON> (vline, hline, bezier)\nhline esetén az y pozíciót a képen (a kép valódi koordinátái szerint\nvline esetén az x pozíciót a képen (a kép valódi koordinátái szerint\nbezier esetén a definiáló pontok koordinátáit\na csoportok neveit\na csoportok elemeit (id alapján)\naz elemekhez (vline, hline, bezier) rendelt skálaértékeket", "inputs": [{"id": 1754938068537, "name": "elem"}, {"id": 1754938068538, "name": "id"}, {"id": 1754938068539, "name": "csoport"}, {"id": 1754938068540, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": []}, {"id": 1754938077683, "name": "ID generálás", "description": "Az ID-t úgy generáljuk, hogy el<PERSON>szö<PERSON> is megnézzük milyen elemről van szó. ez adja az id első karakterét.\n\nvline - v\nhline - h\nbezier - b\n\nEzt követi egy egyszerű 3 számjegyű számsor 000 tól 999 ig.", "inputs": [{"id": 1754938266819, "name": "elem f<PERSON>"}], "outputs": [{"id": 1754938267819, "name": "id"}]}, {"id": 1754938502746, "name": "Csoportnév hozzárendelése gomb", "description": "<PERSON> van aktív ki<PERSON>, a gomb aktívvá válik.\nmegnyomva a gombot felugrik egy popup ablak, melynek az input mezejébe beírja a felhasználó a hozzárendelni kívánt csoportnevet.", "inputs": [{"id": 1754938893843, "name": "kijelölt elemek"}], "outputs": [{"id": 1754938894843, "name": "hozzárendelt csoportnév"}]}, {"id": 1754938916321, "name": "Skálaérték hozzáadása", "description": "Egyszerre csak 1 elem kijelölése esetén aktív a gomb.\nMegnyomva a popup ablak inputjába beírjuk a skálaértéket. Ez csak szám lehet. A popup fejléce: \"Give a defining value/scale value\"", "inputs": [{"id": 1754939203090, "name": "kijelölt elem"}], "outputs": [{"id": 1754939204090, "name": "hozzárendelt skálaérték"}]}, {"id": 1754939429491, "name": "Kijelölés eszköz", "description": "Kijelölés módjai:\n<PERSON><PERSON><PERSON><PERSON><PERSON> az elemre, ctrl/cmd lenyomásá<PERSON> több is kijelölhető.\nBox select: meglévő kijelölést nem törli, hanem hozz<PERSON>.\nHa mellékattintunk ctrl/cmd lenyomása nélkül, akkor a a kijelölés törlődik.\nA kurzort az elem fölé tartva hover effect, hogy lássuk, a kattintás ki fogja jelölni azt. A kijelölt elemeket külön színnel jelöljük.\nHa egyetlen elemet jelölünk ki, akkor arra jobbklikkelve a kijelölés eszközzel előjön egy szerkesztés menüpont, melyet megnyomva szerkeszthetjük\nvline vagy hline esetén a pozícióját, bezier esetén a pontok helyzetét.", "inputs": [], "outputs": [{"id": 1754940351174, "name": "kijel<PERSON><PERSON>s"}]}, {"id": 1754939811652, "name": "Függőleges egyenes hozzáadása gomb", "description": "ezt megnyomva a kurzor egy keresztté változik és egy áttetsző függőleges vonalat húz magával a vásznon. Ahol épp kattint a felhasználó, oda letesz egy függőleges vonalat.", "inputs": [], "outputs": [{"id": 1754940419317, "name": "függőleges egyenes"}]}, {"id": 1754939916665, "name": "Mode announciator", "description": "Egy egyszerű kiírás a jelenleg használt eszközről. hintet is adhat.", "inputs": [], "outputs": []}, {"id": 1754940466765, "name": "Vízszintes egyenes hozzáadása gomb", "description": "ezt megnyomva a kurzor egy keresztté változik és egy áttetsző vízszintes vonalat húz magával a vásznon. Ahol épp kattint a felhasználó, oda letesz egy vízszintes vonalat.", "inputs": [], "outputs": [{"id": 1754941083373, "name": "vízszintes egyenes"}]}, {"id": 1754941059301, "name": "<PERSON><PERSON> görbe hozz<PERSON>adása gomb", "description": "A felhasználó kontrollpontokkal tudja megadni a görbét. Akármennyi pontot lerakhat. ahogy lerakja őket folyamatosan frissüljön a görbe. Az eszközzel az éppen rajzolt görbének a már meglévő pontjait is mozgathatja, finomhangolhatja, ha közel viszi az egeret a ponthoz, akkor az felvillan és meg tudja fogni és mozgatni.", "inputs": [], "outputs": [{"id": 1754941563756, "name": "bezier görbe"}]}, {"id": 1754941597908, "name": "<PERSON><PERSON><PERSON>", "description": "Ez egy lista az egyes elemekkel. Olyasmi mint a réteg panel photoshopban. A jobb s<PERSON><PERSON><PERSON> he<PERSON>ez<PERSON> el. Felsorolja az elemeket id alapján. Mindig frissül. Ha kijelöljük az adott elemet a canvason, akkor itt is highlight<PERSON><PERSON><PERSON><PERSON>, de innen is ki lehet jelölni (fordított működés).\nHa csoportba rendezünk több elemet, akkor a csoporton belül jelenítjük meg a listában (lenyíló csoportmappa). A skálaértéket az id-től jobbra jelezzük. A kijelölt alakzatokat ennek a listának az alján levő kuka ikonnal lehet törölni.\n", "inputs": [{"id": 1754941923887, "name": "id-k"}, {"id": 1754941923888, "name": "csoportok"}, {"id": 1754941923889, "name": "sk<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "outputs": []}, {"id": 1754941959253, "name": "<PERSON><PERSON>", "description": "<PERSON>z egy fehé<PERSON> canvas, amely<PERSON> a rajzolás történik.", "inputs": [{"id": 1754942019928, "name": "<PERSON><PERSON><PERSON><PERSON>rk<PERSON><PERSON>"}, {"id": 1754942019929, "name": "elemek"}], "outputs": []}], "connections": [{"from": {"nodeId": 1754938077683, "outputId": 1754938267819, "outputName": "id"}, "to": {"nodeId": 1754937318913, "inputId": 1754938068538, "inputName": "id"}}, {"from": {"nodeId": 1754938502746, "outputId": 1754938894843, "outputName": "hozzárendelt csoportnév"}, "to": {"nodeId": 1754937318913, "inputId": 1754938068539, "inputName": "csoport"}}, {"from": {"nodeId": 1754938916321, "outputId": 1754939204090, "outputName": "hozzárendelt skálaérték"}, "to": {"nodeId": 1754937318913, "inputId": 1754938068540, "inputName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"from": {"nodeId": 1754939429491, "outputId": 1754940351174, "outputName": "kijel<PERSON><PERSON>s"}, "to": {"nodeId": 1754938502746, "inputId": 1754938893843, "inputName": "kijelölt elemek"}}, {"from": {"nodeId": 1754939429491, "outputId": 1754940351174, "outputName": "kijel<PERSON><PERSON>s"}, "to": {"nodeId": 1754938916321, "inputId": 1754939203090, "inputName": "kijelölt elem"}}, {"from": {"nodeId": 1754939811652, "outputId": 1754940419317, "outputName": "függőleges egyenes"}, "to": {"nodeId": 1754937318913, "inputId": 1754938068537, "inputName": "elem"}}, {"from": {"nodeId": 1754940466765, "outputId": 1754941083373, "outputName": "vízszintes egyenes"}, "to": {"nodeId": 1754937318913, "inputId": 1754938068537, "inputName": "elem"}}, {"from": {"nodeId": 1754941059301, "outputId": 1754941563756, "outputName": "bezier görbe"}, "to": {"nodeId": 1754937318913, "inputId": 1754938068537, "inputName": "elem"}}, {"from": {"nodeId": 1754939811652, "outputId": 1754940419317, "outputName": "függőleges egyenes"}, "to": {"nodeId": 1754938077683, "inputId": 1754938266819, "inputName": "elem f<PERSON>"}}, {"from": {"nodeId": 1754940466765, "outputId": 1754941083373, "outputName": "vízszintes egyenes"}, "to": {"nodeId": 1754938077683, "inputId": 1754938266819, "inputName": "elem f<PERSON>"}}, {"from": {"nodeId": 1754941059301, "outputId": 1754941563756, "outputName": "bezier görbe"}, "to": {"nodeId": 1754938077683, "inputId": 1754938266819, "inputName": "elem f<PERSON>"}}, {"from": {"nodeId": 1754938077683, "outputId": 1754938267819, "outputName": "id"}, "to": {"nodeId": 1754941597908, "inputId": 1754941923887, "inputName": "id-k"}}, {"from": {"nodeId": 1754938502746, "outputId": 1754938894843, "outputName": "hozzárendelt csoportnév"}, "to": {"nodeId": 1754941597908, "inputId": 1754941923888, "inputName": "csoportok"}}, {"from": {"nodeId": 1754938916321, "outputId": 1754939204090, "outputName": "hozzárendelt skálaérték"}, "to": {"nodeId": 1754941597908, "inputId": 1754941923889, "inputName": "sk<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"from": {"nodeId": 1754937315274, "outputId": 1754937533938, "outputName": "k<PERSON>p"}, "to": {"nodeId": 1754941959253, "inputId": 1754942019928, "inputName": "<PERSON><PERSON><PERSON><PERSON>rk<PERSON><PERSON>"}}, {"from": {"nodeId": 1754939811652, "outputId": 1754940419317, "outputName": "függőleges egyenes"}, "to": {"nodeId": 1754941959253, "inputId": 1754942019929, "inputName": "elemek"}}, {"from": {"nodeId": 1754940466765, "outputId": 1754941083373, "outputName": "vízszintes egyenes"}, "to": {"nodeId": 1754941959253, "inputId": 1754942019929, "inputName": "elemek"}}, {"from": {"nodeId": 1754941059301, "outputId": 1754941563756, "outputName": "bezier görbe"}, "to": {"nodeId": 1754941959253, "inputId": 1754942019929, "inputName": "elemek"}}], "groups": [{"id": 1754937341828, "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "A fejléc a fájllal és megjelenítéssel kapcsolatos információkat és kezelőszerveket tartalmazza.", "nodes": [1754937315274, 1754937318913, 1754939916665]}, {"id": 1754939304660, "name": "Akció toolbar", "description": "A bal szélen függőlegesen elhelyezkedő minimalista, ikonokkal ellátott toolbar.", "nodes": [1754938502746, 1754938916321]}, {"id": 1754939814779, "name": "<PERSON><PERSON><PERSON>", "description": "Az akció toolbar fölött elhelyezkedő hasonló stílusú toolbar. ESC megnyomásával kilépünk az eszközből. Az aktív eszközt a fejlécben jelezzük. Egyszerre egyetlen eszközt használhatunk.", "nodes": [1754939429491, 1754939811652, 1754940466765, 1754941059301]}]}